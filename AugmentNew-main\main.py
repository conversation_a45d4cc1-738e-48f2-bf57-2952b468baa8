#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AugmentNew 主程序入口
安全启动，完整的错误处理和系统保��
"""

import os
import sys
import traceback
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志系统"""
    try:
        log_dir = project_root / "logs"
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / "augment_new.log", encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        logger = logging.getLogger(__name__)
        logger.info("=== AugmentNew 启动 ===")
        return logger
        
    except Exception as e:
        print(f"日志系统初始化失败: {e}")
        return None

def check_system_safety():
    """系统安全检查"""
    try:
        # 检查系统资源
        import psutil
        
        # 检查内存使用率
        memory = psutil.virtual_memory()
        if memory.percent > 90:
            print("⚠️ 警告: 系统内存使用率过高 (>90%)，建议关闭其他程序")
            return False
        
        # 检查磁盘空间
        disk = psutil.disk_usage('/')
        if disk.percent > 95:
            print("⚠️ 警告: 磁盘空间不足 (<5%)，可能影响程序运行")
            return False
        
        return True
        
    except ImportError:
        print("📦 psutil 未安装，跳过系统资源检查")
        return True
    except Exception as e:
        print(f"⚠�� 系统安全检查失败: {e}")
        return True  # 不阻止程序启动

def create_backup_point():
    """创建启动��备份点"""
    try:
        backup_dir = project_root / "emergency_backups"
        backup_dir.mkdir(exist_ok=True)
        
        import time
        backup_name = f"startup_backup_{int(time.time())}"
        backup_path = backup_dir / backup_name
        backup_path.mkdir(exist_ok=True)
        
        # 备份关键配置文件
        config_files = [
            "super_config.json",
            "version.txt"
        ]
        
        for config_file in config_files:
            config_path = project_root / config_file
            if config_path.exists():
                import shutil
                shutil.copy2(config_path, backup_path / config_file)
        
        print(f"✅ 启动备份点已创建: {backup_name}")
        return True
        
    except Exception as e:
        print(f"⚠️ 创建备份点失败: {e}")
        return False

def check_dependencies():
    """检查依赖包"""
    required_packages = {
        'customtkinter': 'customtkinter',
        'PIL': 'PIL',
        'requests': 'requests'
    }

    missing_packages = []

    for import_name, package_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    if missing_packages:
        print("❌ 缺少必需的依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 请运行以下命令安装:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False

    return True

def safe_import_gui():
    """安全导入GUI模块"""
    try:
        from gui.main_window import MainWindow
        return MainWindow
    except ImportError as e:
        print(f"❌ GUI模块导入失败: {e}")
        print("🔧 尝试使用备用启动方式...")
        
        try:
            # 尝试备用导入方式
            import gui_main
            return gui_main.MainWindow
        except ImportError:
            print("❌ 备用GUI模块也���法导入")
            return None
    except Exception as e:
        print(f"❌ GUI模块导入异常: {e}")
        return None

def emergency_mode():
    """紧急模式 - 命令行界面"""
    print("\n🚨 进入紧急模式 - 命令行界面")
    print("=" * 50)
    
    while True:
        print("\n🔧 AugmentNew 紧急模式")
        print("1. 一键清理")
        print("2. 超级重置")
        print("3. 系统检查")
        print("4. 修复程序")
        print("5. 退出")
        
        try:
            choice = input("\n请选择操作 (1-5): ").strip()
            
            if choice == '1':
                emergency_clean()
            elif choice == '2':
                emergency_reset()
            elif choice == '3':
                emergency_check()
            elif choice == '4':
                emergency_repair()
            elif choice == '5':
                print("👋 退出程序")
                break
            else:
                print("❌ 无效选择，��重试")
                
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")

def emergency_clean():
    """紧急清理功能"""
    try:
        print("\n🧹 执行紧急清理...")
        
        # 导入清理模块
        from augutils.json_modifier import modify_telemetry_ids
        from augutils.sqlite_modifier import clean_augment_data
        from augutils.workspace_cleaner import clean_workspace_storage
        
        # 执行清理
        print("🔧 修改Telemetry ID...")
        modify_telemetry_ids()
        
        print("🔧 清理数据库...")
        clean_augment_data()
        
        print("�� 清理工作区...")
        clean_workspace_storage()
        
        print("✅ 紧急清理完成！")
        
    except Exception as e:
        print(f"❌ 紧急清理失败: {e}")

def emergency_reset():
    """紧急重置功能"""
    try:
        print("\n💥 执行紧急重置...")
        
        from utils.super_reset_engine import SuperResetEngine
        
        engine = SuperResetEngine()
        result = engine.nuclear_reset()
        
        if result['success']:
            print("✅ 紧急重置完成！")
        else:
            print("❌ 紧急重置失败")
            for error in result['errors']:
                print(f"   - {error}")
                
    except Exception as e:
        print(f"❌ 紧急重置失败: {e}")

def emergency_check():
    """紧急系统检查"""
    print("\n🔍 执行系统检查...")
    
    # 检查文件完整���
    critical_files = [
        "gui/main_window.py",
        "augutils/__init__.py",
        "utils/__init__.py"
    ]
    
    missing_files = []
    for file_path in critical_files:
        if not (project_root / file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 发现缺失文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
    else:
        print("✅ 关键文件完整")
    
    # 检查权限
    if os.access(project_root, os.W_OK):
        print("✅ 目录写入权限正常")
    else:
        print("❌ 目录写入权限��足")

def emergency_repair():
    """紧急修复功能"""
    print("\n🔧 执行紧急修复...")
    
    try:
        # ���新创建必要目录
        dirs_to_create = ["logs", "backups", "temp", "emergency_backups"]
        for dir_name in dirs_to_create:
            dir_path = project_root / dir_name
            dir_path.mkdir(exist_ok=True)
            print(f"✅ 目��已创建/检查: {dir_name}")
        
        # 重新创建__init__.py文件
        init_files = [
            "gui/__init__.py",
            "augutils/__init__.py", 
            "utils/__init__.py"
        ]
        
        for init_file in init_files:
            init_path = project_root / init_file
            if not init_path.exists():
                init_path.write_text("# -*- coding: utf-8 -*-\n", encoding='utf-8')
                print(f"✅ 已创建: {init_file}")
        
        print("✅ 紧急修复完成！")
        
    except Exception as e:
        print(f"❌ 紧急修复失败: {e}")

def main():
    """���函数 - 安全启动流程"""
    try:
        print("🚀 AugmentNew 启动中...")
        print("=" * 50)
        
        # 1. 设置日志
        logger = setup_logging()
        
        # 2. 系统安全检查
        print("🔍 执行系统安全检查...")
        if not check_system_safety():
            response = input("⚠️ 系统资源不足，是否继续启��？(y/N): ")
            if response.lower() != 'y':
                print("👋 用户取消启动")
                return
        
        # 3. 创建备份点
        print("💾 创建启动备份点...")
        create_backup_point()
        
        # 4. 检查依赖
        print("📦 检查依赖包...")
        if not check_dependencies():
            print("❌ 依赖���查失败，无法启动GUI")
            emergency_mode()
            return
        
        # 5. 安全导入GUI
        print("🎨 加载图形界面...")
        MainWindow = safe_import_gui()
        
        if MainWindow is None:
            print("❌ GUI加载失败，启动紧急模式")
            emergency_mode()
            return
        
        # 6. 启动GUI
        print("✅ 启动图形界面...")
        app = MainWindow()
        app.mainloop()
        
    except KeyboardInterrupt:
        print("\n👋 用户中断启动")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("\n📋 错误详情:")
        traceback.print_exc()
        
        print("\n🚨 启动紧急模式...")
        emergency_mode()

if __name__ == "__main__":
    main()
